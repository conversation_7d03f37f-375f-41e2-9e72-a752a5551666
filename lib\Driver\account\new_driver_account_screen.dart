import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/Driver/profile/driver_profile_screen.dart';
import 'package:sepesha_app/Driver/wallet/presentation/wallet_screen.dart';
import 'package:sepesha_app/l10n/app_localizations.dart';
import 'package:sepesha_app/models/user_data.dart';
import 'package:sepesha_app/repositories/user_profile_repository.dart';
import 'package:sepesha_app/screens/about_screen.dart';
import 'package:sepesha_app/screens/conversation_list_screen.dart';
import 'package:sepesha_app/screens/auth/support/support_screen.dart';
import 'package:sepesha_app/screens/auth/auth_screen.dart';
import 'package:sepesha_app/screens/settings_screen.dart';
import 'package:sepesha_app/services/auth_services.dart';

class NewDriverAccountScreen extends StatefulWidget {
  const NewDriverAccountScreen({super.key});

  @override
  State<NewDriverAccountScreen> createState() => _NewDriverAccountScreenState();
}

class _NewDriverAccountScreenState extends State<NewDriverAccountScreen> with TickerProviderStateMixin {
  //User? driverData;
  UserData? driverData;
  bool isLoading = true;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDriverData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  Future<void> _loadDriverData() async {
  try {
    // Use UserProfileRepository instead of DashboardRepository
    final profileData = await UserProfileRepository().getUserProfile();
    setState(() {
      driverData = profileData?['user'] as UserData?;
      isLoading = false;
    });
  } catch (e) {
    setState(() {
      isLoading = false;
      driverData = UserData(
        firstName: 'Driver',
        lastName: 'User',
        phoneNumber: '+255000000000',
        email: '<EMAIL>',
        password: '',
        userType: 'driver',
        regionId: 1,
        profilePhotoUrl: null,
        isVerified: false,
      );
    });
  }
}

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localozations = AppLocalizations.of(context)!;
    if (isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
        ),
      );
    }

    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: RefreshIndicator(
        onRefresh: _loadDriverData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  _buildEnhancedDriverProfileHeader(),
                  
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 12, 20, 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /*// Driver Stats Section
                        _buildDriverStatsSection(),
                        const SizedBox(height: 24),

                        // Vehicle Information Card
                        _buildVehicleInfoCard(),
                        const SizedBox(height: 24),

                        // Payment Preference Card
                        _buildPaymentPreferenceCard(),
                        const SizedBox(height: 32),*/

                        // Profile & Settings Section
                        _buildEnhancedMenuItem(
                          icon: Icons.person_outline_rounded,
                          title: localozations.driverProfile,
                          subtitle: localozations.manageDriverProfile,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const DriverProfileScreen()),
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.account_balance_wallet_rounded,
                          title: localozations.paymentMethods,
                          subtitle: localozations.managePaymentOptions,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const WalletScreen()),
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.chat_bubble_outline_rounded,
                          title: localozations.messages,
                          subtitle: localozations.viewConversations,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const ConversationsScreen()),
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.settings_rounded,
                          title: localozations.settings,
                          subtitle: localozations.appPreferencesSettings,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const SettingsScreen()),
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.help_outline_rounded,
                          title: localozations.helpSupport,
                          subtitle: localozations.getHelpSupport,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const SupportScreen()),
                          ),
                        ),
                         const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.info_outline_rounded,
                          title: localozations.about,
                          subtitle: localozations.appInformationVersion,
                          color: AppColor.primaryColor,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const AboutScreen()),
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildEnhancedMenuItem(
                          icon: Icons.logout_rounded,
                          title: localozations.logout,
                          subtitle: localozations.signOutAccount,
                          color: AppColor.primaryColor,
                          onTap: () => _showLogoutDialog(),
                          isDestructive: true,
                        ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedDriverProfileHeader() {
    final localozations = AppLocalizations.of(context)!;
    return Container(
      width: double.infinity,
      color: AppColor.primaryColor,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(24, 20, 24, 32),
          child: Column(
            children: [
              // Profile Avatar and Info
              Row(
                children: [
                  Hero(
                    tag: 'driver_profile_avatar',
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: CircleAvatar(
                        radius: 42,
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        child: CircleAvatar(
                          radius: 38,
                          backgroundColor: Theme.of(context).colorScheme.surface,
                          backgroundImage: driverData?.profilePhotoUrl != null
                              ? NetworkImage(driverData!.profilePhotoUrl!)
                              : null,
                          child: driverData?.profilePhotoUrl == null
                              ? Icon(
                                  Icons.person_rounded,
                                  size: 45,
                                  color: AppColor.primaryColor,
                                )
                              : null,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),

                  // Driver Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${driverData?.firstName ?? localozations.driver} ${driverData?.lastName ?? ''}',
                          style: AppTextStyle.heading2(Theme.of(context).colorScheme.onSurface).copyWith(
                            fontWeight: FontWeight.w700,
                            shadows: [
                              Shadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                offset: const Offset(0, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        // Driver Badge and Rating
                        Row(
                          children: [
                           Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 16,
                                    color: (driverData?.isVerified ?? false) ? Colors.green : Colors.grey,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    (driverData?.isVerified ?? false) ? localozations.verified : localozations.unverified,
                                    style: AppTextStyle.caption(Theme.of(context).colorScheme.onSurface).copyWith(
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            // Rating
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.amber.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.star, color: Colors.amber, size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    driverData?.averageRating?.toStringAsFixed(1) ?? '0.0',
                                    style: AppTextStyle.caption(Theme.of(context).colorScheme.onSurface).copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: TextButton(
        onPressed: onTap,
        style: TextButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.surface,
          foregroundColor: Theme.of(context).colorScheme.onSurface, 
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          alignment: Alignment.centerLeft,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: isDestructive ? Colors.red : color,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.paragraph1(Theme.of(context).colorScheme.onSurface).copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTextStyle.subtext1(Theme.of(context).colorScheme.onSurface),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    final localozations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.logout_rounded, color: Colors.red, size: 28),
              const SizedBox(width: 12),
              Text(
                localozations.logout,
                style: AppTextStyle.heading3(AppColor.blackText),
              ),
            ],
          ),
          content: Text(
            localozations.logoutConfirmation,
            style: AppTextStyle.paragraph1(AppColor.grey),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                localozations.cancel,
                style: AppTextStyle.paragraph2(AppColor.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                if (mounted) {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const AuthScreen()),
                    (route) => false,
                  );
                }
                AuthServices.logout(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: AppColor.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                localozations.logout,
                style: AppTextStyle.paragraph2(AppColor.white).copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getDriverInitials() {
    final firstName = driverData?.firstName ?? 'D';
    final lastName = driverData?.lastName ?? '';
    
    if (firstName.isNotEmpty && lastName.isNotEmpty) {
      return '${firstName[0]}${lastName[0]}'.toUpperCase();
    } else if (firstName.isNotEmpty) {
      return firstName[0].toUpperCase();
    }
    return 'D';
  } 
}
