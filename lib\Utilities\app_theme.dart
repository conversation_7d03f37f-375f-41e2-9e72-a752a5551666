import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart' show FlexSchemeColor, FlexThemeData, FlexSurfaceMode, FlexColorScheme;
import 'package:sepesha_app/Utilities/app_color.dart';

class AppTheme {
  static ThemeData lightTheme(BuildContext context) {
    return FlexThemeData.light(
      colors: FlexSchemeColor.from(
        primary: AppColor.primary,
        secondary: AppColor.secondary,
      ),
      surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
      blendLevel: 7,
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
    ).copyWith(
      scaffoldBackgroundColor: Theme.of(context).colorScheme.background,
      appBarTheme: AppBarTheme(
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
      ),
    );
  }

  static ThemeData darkTheme(BuildContext context) {
    return FlexThemeData.dark(
      colors: FlexSchemeColor.from(
        primary: AppColor.primary,
        secondary: AppColor.secondary,
      ),
      surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
      blendLevel: 13,
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
    ).copyWith(
      scaffoldBackgroundColor: Theme.of(context).colorScheme.background,
      appBarTheme: AppBarTheme(
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
      ),
    );
  }
}