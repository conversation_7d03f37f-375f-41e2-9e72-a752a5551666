import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart' show FlexSchemeColor, FlexThemeData, FlexSurfaceMode, FlexColorScheme;
import 'package:sepesha_app/Utilities/app_color.dart';

class AppTheme {
ThemeData lightTheme(BuildContext context) {
  final flexTheme = FlexThemeData.light(
    colors: FlexSchemeColor.from(
      primary: AppColor.primary,
      secondary: AppColor.secondary,
    ),
    surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
    blendLevel: 7,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
  );
  
  return flexTheme.copyWith(
    scaffoldBackgroundColor: flexTheme.colorScheme.background, 
    appBarTheme: AppBarTheme(
      backgroundColor: flexTheme.colorScheme.surface,           
      foregroundColor: flexTheme.colorScheme.onSurface,        
      elevation: 0,
    ),
  );
}

ThemeData darkTheme(BuildContext context) {
  final flexTheme = FlexThemeData.dark(
    colors: FlexSchemeColor.from(
      primary: AppColor.primary,
      secondary: AppColor.secondary,
    ),
    surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
    blendLevel: 13,
    visualDensity: FlexColorScheme.comfortablePlatformDensity,
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
  );  
  
  return flexTheme.copyWith(
    scaffoldBackgroundColor: flexTheme.colorScheme.background, 
    appBarTheme: AppBarTheme(
      backgroundColor: flexTheme.colorScheme.surface,           
      foregroundColor: flexTheme.colorScheme.onSurface,        
      elevation: 0,
    ),
  );
}
}