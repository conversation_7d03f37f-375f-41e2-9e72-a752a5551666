-Dflutter.dart_plugin_registrant=file:///C:/Users/<USER>/Desktop/Sepesha_app/.dart_tool/flutter_build/dart_plu
gin_registrant.dart --verbosity=error package:sepesha_app/main.dart
[   +7 ms] [+24032 ms]
../../AppData/Local/Pub/Cache/hosted/pub.dev/flex_color_scheme-7.3.1/lib/src/flex_color_scheme.dart:6826:11: Error:  
The argument type 'CardTheme?' can't be assigned to the parameter type 'CardThemeData?'.
[   +1 ms] [   +3 ms]  - 'CardTheme' is from 'package:flutter/src/material/card_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/card_theme.dart').
[   +6 ms] [        ]  - 'CardThemeData' is from 'package:flutter/src/material/card_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/card_theme.dart').
[        ] [        ]           ? FlexSubThemes.cardTheme(
[        ] [        ]           ^
[        ] [        ]
../../AppData/Local/Pub/Cache/hosted/pub.dev/flex_color_scheme-7.3.1/lib/src/flex_color_scheme.dart:6899:11: Error:  
The argument type 'DialogTheme?' can't be assigned to the parameter type 'DialogThemeData?'.
[   +5 ms] [   +1 ms]  - 'DialogTheme' is from 'package:flutter/src/material/dialog_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/dialog_theme.dart').
[   +1 ms] [        ]  - 'DialogThemeData' is from 'package:flutter/src/material/dialog_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/dialog_theme.dart').
[   +1 ms] [        ]           ? FlexSubThemes.dialogTheme(
[        ] [        ]           ^
[   +6 ms] [        ]
../../AppData/Local/Pub/Cache/hosted/pub.dev/flex_color_scheme-7.3.1/lib/src/flex_color_scheme.dart:7290:34: Error:  
The argument type 'TabBarTheme' can't be assigned to the parameter type 'TabBarThemeData?'.
[   +1 ms] [        ]  - 'TabBarTheme' is from 'package:flutter/src/material/tab_bar_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart').
[  +13 ms] [        ]  - 'TabBarThemeData' is from 'package:flutter/src/material/tab_bar_theme.dart'
('../../AppData/Local/Android/flutter/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart').
[   +1 ms] [        ]       tabBarTheme: FlexSubThemes.tabBarTheme(
[        ] [        ]                                  ^
[+13261 ms] [+16591 ms] Persisting file store
[   +2 ms] [   +9 ms] Done persisting file store
[   +1 ms] [   +7 ms] Target kernel_snapshot_program failed: Exception
[        ]            #0      KernelSnapshot.build (package:flutter_tools/src/build_system/targets/common.dart:285:7)
[   +8 ms]            <asynchronous suspension>
[   +1 ms]            #1      _BuildInstance._invokeInternal
(package:flutter_tools/src/build_system/build_system.dart:873:9)
[   +5 ms]            <asynchronous suspension>
[        ]            #2      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
[        ]            <asynchronous suspension>
[        ]            #3      _BuildInstance.invokeTarget
(package:flutter_tools/src/build_system/build_system.dart:811:32)
[   +1 ms]            <asynchronous suspension>
[        ]            #4      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
[   +2 ms]            <asynchronous suspension>
[        ]            #5      _BuildInstance.invokeTarget
(package:flutter_tools/src/build_system/build_system.dart:811:32)
[   +1 ms]            <asynchronous suspension>
[        ]            #6      Future.wait.<anonymous closure> (dart:async/future.dart:525:21)
[        ]            <asynchronous suspension>
[   +3 ms]            #7      _BuildInstance.invokeTarget
(package:flutter_tools/src/build_system/build_system.dart:811:32)
[   +2 ms]            <asynchronous suspension>
[   +4 ms]            #8      FlutterBuildSystem.build
(package:flutter_tools/src/build_system/build_system.dart:629:16)
[   +2 ms]            <asynchronous suspension>
[  +22 ms]            #9      AssembleCommand.runCommand (package:flutter_tools/src/commands/assemble.dart:346:32)
[   +1 ms]            <asynchronous suspension>
[   +1 ms]            #10     FlutterCommand.run.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command.dart:1563:27)
[   +4 ms]            <asynchronous suspension>
[   +1 ms]            #11     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[   +2 ms]            <asynchronous suspension>
[   +1 ms]            #12     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
[   +1 ms]            <asynchronous suspension>
[   +4 ms]            #13     FlutterCommandRunner.runCommand.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
[  +13 ms]            <asynchronous suspension>
[        ]            #14     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[   +4 ms]            <asynchronous suspension>
[   +7 ms]            #15     FlutterCommandRunner.runCommand
(package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
[   +1 ms]            <asynchronous suspension>
[        ]            #16     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)  
[   +2 ms]            <asynchronous suspension>
[   +1 ms]            #17     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[        ]            <asynchronous suspension>
[        ]            #18     main (package:flutter_tools/executable.dart:102:3)
[        ]            <asynchronous suspension>
[   +8 ms] [  +19 ms] "flutter assemble" took 45,103ms.
[ +265 ms] [ +370 ms] 
[        ]            #0      throwToolExit (package:flutter_tools/src/base/common.dart:34:3)
[        ]            #1      AssembleCommand.runCommand (package:flutter_tools/src/commands/assemble.dart:365:7)    
[        ]            <asynchronous suspension>
[        ]            #2      FlutterCommand.run.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command.dart:1563:27)
[   +1 ms]            <asynchronous suspension>
[        ]            #3      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[        ]            <asynchronous suspension>
[        ]            #4      CommandRunner.runCommand (package:args/command_runner.dart:212:13)
[        ]            <asynchronous suspension>
[        ]            #5      FlutterCommandRunner.runCommand.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
[        ]            <asynchronous suspension>
[        ]            #6      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[   +1 ms]            <asynchronous suspension>
[        ]            #7      FlutterCommandRunner.runCommand
(package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
[   +1 ms]            <asynchronous suspension>
[        ]            #8      run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)  
[        ]            <asynchronous suspension>
[   +1 ms]            #9      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
[   +1 ms]            <asynchronous suspension>
[   +1 ms]            #10     main (package:flutter_tools/executable.dart:102:3)
[   +1 ms]            <asynchronous suspension>
[   +1 ms] [   +1 ms] Running 1 shutdown hook
[        ] [   +2 ms] Shutdown hooks complete
[ +282 ms] [ +281 ms] exiting with code 1
[   +1 ms] > Task :app:compileFlutterBuildDebug FAILED
[  +94 ms] FAILURE: Build failed with an exception.
[   +1 ms] * What went wrong:
[   +2 ms] Execution failed for task ':app:compileFlutterBuildDebug'.
[        ] > Process 'command 'C:\Users\<USER>\AppData\Local\Android\flutter\flutter\bin\flutter.bat'' finished 
with non-zero exit value 1
[   +1 ms] * Try:
[   +1 ms] > Run with --debug option to get more log output.
[        ] > Run with --scan to get full insights.
[        ] > Get more help at https://help.gradle.org.
[  +11 ms] * Exception is:
[   +3 ms] org.gradle.api.tasks.TaskExecutionException: Execution failed for task ':app:compileFlutterBuildDebug'.   
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.lambda$executeIfValid$1(ExecuteActionsTaskExecuter
.java:130)
[  +15 ms]      at org.gradle.internal.Try$Failure.ifSuccessfulOrElse(Try.java:293)
[   +8 ms]      at
org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:128
)
[   +7 ms]      at
org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)      
[  +14 ms]      at
org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46
)
[        ]      at
org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.jav
a:51)
[   +3 ms]      at
org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57) 
[   +1 ms]      at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
[   +8 ms]      at
org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)       
[   +6 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)       
[ +176 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
[   +2 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:209)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:204)
[  +17 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
[  +12 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[   +1 ms]      at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
[        ]      at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
[        ]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.
java:331)
[  +10 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.
java:318)
[   +3 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultT
askExecutionGraph.java:314)
[        ]      at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)    
[        ]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecut
ionGraph.java:314)
[        ]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecut
ionGraph.java:303)
[        ]      at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
[   +5 ms]      at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)    
[        ]      at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
[   +1 ms]      at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)     
[  +18 ms]      at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
[   +2 ms]      at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
[   +1 ms]      at java.base/java.lang.Thread.run(Unknown Source)
[        ] Caused by: org.gradle.process.internal.ExecException: Process 'command 'C:\Users\<USER>\AppData\Local\Android\flutter\flutter\bin\flutter.bat'' finished with non-zero exit value 1
[        ]      at
org.gradle.process.internal.DefaultExecHandle$ExecResultImpl.assertNormalExitValue(DefaultExecHandle.java:442)       
[   +1 ms]      at org.gradle.process.internal.DefaultExecAction.execute(DefaultExecAction.java:38)
[  +14 ms]      at org.gradle.process.internal.DefaultExecActionFactory.exec(DefaultExecActionFactory.java:202)
[        ]      at org.gradle.process.internal.DefaultExecOperations.exec(DefaultExecOperations.java:37)
[  +14 ms]      at com.flutter.gradle.tasks.BaseFlutterTaskHelper.buildBundle(BaseFlutterTaskHelper.kt:174)
[   +2 ms]      at com.flutter.gradle.tasks.BaseFlutterTask.buildBundle(BaseFlutterTask.kt:147)
[  +13 ms]      at com.flutter.gradle.tasks.FlutterTaskHelper.build$gradle(FlutterTaskHelper.kt:90)
[   +2 ms]      at com.flutter.gradle.tasks.FlutterTask.build(FlutterTask.kt:49)
[   +1 ms]      at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
[        ]      at java.base/java.lang.reflect.Method.invoke(Unknown Source)
[   +9 ms]      at org.gradle.internal.reflect.JavaMethod.invoke(JavaMethod.java:125)
[   +5 ms]      at org.gradle.api.internal.project.taskfactory.StandardTaskAction.doExecute(StandardTaskAction.java:58)
[        ]      at org.gradle.api.internal.project.taskfactory.StandardTaskAction.execute(StandardTaskAction.java:51)
[        ]      at org.gradle.api.internal.project.taskfactory.StandardTaskAction.execute(StandardTaskAction.java:29)
[        ]      at org.gradle.api.internal.tasks.execution.TaskExecution$3.run(TaskExecution.java:244)
[   +4 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[   +2 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[   +3 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.api.internal.tasks.execution.TaskExecution.executeAction(TaskExecution.java:229)       
[        ]      at org.gradle.api.internal.tasks.execution.TaskExecution.executeActions(TaskExecution.java:212)      
[   +2 ms]      at
org.gradle.api.internal.tasks.execution.TaskExecution.executeWithPreviousOutputFiles(TaskExecution.java:195)
[   +1 ms]      at org.gradle.api.internal.tasks.execution.TaskExecution.execute(TaskExecution.java:162)
[        ]      at org.gradle.internal.execution.steps.ExecuteStep.executeInternal(ExecuteStep.java:105)
[        ]      at org.gradle.internal.execution.steps.ExecuteStep.access$000(ExecuteStep.java:44)
[        ]      at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:59)
[        ]      at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:56)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:209)
[   +5 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:204)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[   +1 ms]      at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
[   +1 ms]      at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:56)
[   +1 ms]      at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:44)
[        ]      at org.gradle.internal.execution.steps.CancelExecutionStep.execute(CancelExecutionStep.java:42)      
[        ]      at org.gradle.internal.execution.steps.TimeoutStep.executeWithoutTimeout(TimeoutStep.java:75)        
[   +4 ms]      at org.gradle.internal.execution.steps.TimeoutStep.execute(TimeoutStep.java:55)
[        ]      at
org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:50)
[   +1 ms]      at
org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:28)
[        ]      at
org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:67)
[        ]      at
org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:37)
[        ]      at
org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:61)       
[   +2 ms]      at
org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:26)       
[        ]      at
org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:69
)
[   +1 ms]      at
org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:46
)
[        ]      at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:40)
[   +3 ms]      at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:29)
[   +1 ms]      at org.gradle.internal.execution.steps.BuildCacheStep.executeWithoutCache(BuildCacheStep.java:189)   
[        ]      at org.gradle.internal.execution.steps.BuildCacheStep.lambda$execute$1(BuildCacheStep.java:75)       
[        ]      at org.gradle.internal.Either$Right.fold(Either.java:175)
[        ]      at org.gradle.internal.execution.caching.CachingState.fold(CachingState.java:62)
[   +1 ms]      at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:73)
[        ]      at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:48)
[        ]      at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:46)
[        ]      at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:35)
[   +1 ms]      at org.gradle.internal.execution.steps.SkipUpToDateStep.executeBecause(SkipUpToDateStep.java:75)     
[        ]      at org.gradle.internal.execution.steps.SkipUpToDateStep.lambda$execute$2(SkipUpToDateStep.java:53)   
[   +1 ms]      at java.base/java.util.Optional.orElseGet(Unknown Source)
[   +1 ms]      at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:53)
[        ]      at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:35)
[   +2 ms]      at
org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedS
tep.java:37)
[   +1 ms]      at
org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedS
tep.java:27)
[   +1 ms]      at
org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingState
Step.java:49)
[        ]      at
org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingState
Step.java:27)
[        ]      at
org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:71) 
[        ]      at
org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:39) 
[        ]      at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:65)        
[        ]      at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:36)        
[        ]      at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:107)
[   +1 ms]      at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:56)
[        ]      at
org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecuti
onStep.java:64)
[   +1 ms]      at
org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecuti
onStep.java:43)
[   +1 ms]      at
org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.executeWithNonEmptySources(AbstractSkipEmptyWorkStep.ja
va:125)
[        ]      at
org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:56)
[        ]      at
org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:36)
[   +1 ms]      at
org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(MarkSnapshottingInputsStartedSte
p.java:38)
[        ]      at
org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:36)   
[   +1 ms]      at
org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:23)   
[        ]      at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:75)
[        ]      at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:41)
[        ]      at
org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.lambda$execute$0(AssignMutableWorkspaceStep.java:35)  
[        ]      at org.gradle.api.internal.tasks.execution.TaskExecution$4.withWorkspace(TaskExecution.java:289)     
[        ]      at
org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:31)
[        ]      at
org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:22)
[        ]      at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:40)        
[        ]      at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:23)        
[        ]      at
org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$2(ExecuteWorkBuildOperationFir
ingStep.java:67)
[   +1 ms]      at java.base/java.util.Optional.orElseGet(Unknown Source)
[        ]      at
org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.j
ava:67)
[   +2 ms]      at
org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.j
ava:39)
[   +7 ms]      at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:46)
[   +2 ms]      at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:34)
[        ]      at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:48)
[        ]      at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:35)
[   +3 ms]      at org.gradle.internal.execution.impl.DefaultExecutionEngine$1.execute(DefaultExecutionEngine.java:61)
[   +3 ms]      at
org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:127
)
[   +6 ms]      at
org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)      
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46
)
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.jav
a:51)
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57) 
[  +15 ms]      at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
[  +12 ms]      at
org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)       
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)       
[   +1 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
[  +17 ms]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:209)
[   +5 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperation
Runner.java:204)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[   +4 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
[   +1 ms]      at
org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[  +14 ms]      at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
[        ]      at
org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
[   +1 ms]      at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
[        ]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.
java:331)
[   +1 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.
java:318)
[   +4 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultT
askExecutionGraph.java:314)
[   +2 ms]      at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)    
[   +6 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecut
ionGraph.java:314)
[   +2 ms]      at
org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecut
ionGraph.java:303)
[   +9 ms]      at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
[   +6 ms]      at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
[   +1 ms]      at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
[        ]      at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)     
[        ]      at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
[   +4 ms]      at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
[   +1 ms]      at java.base/java.lang.Thread.run(Unknown Source)
[   +1 ms] BUILD FAILED in 58s
[   +4 ms] Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.
[   +2 ms] You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come
from your own scripts or plugins.
[   +1 ms] For more on this, please refer to
https://docs.gradle.org/8.10.2/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle
documentation.
[   +1 ms] 7 actionable tasks: 3 executed, 4 up-to-date
[        ] Watched directory hierarchies: [C:\Users\<USER>\AppData\Local\Android\flutter\flutter\packages\flutter_tools\gradle, C:\Users\<USER>\Desktop\Sepesha_app\android]
[  +28 ms] Running Gradle task 'assembleDebug'... (completed in 59.4s)
[ +116 ms] Error: Gradle task assembleDebug failed with exit code 1
[   +4 ms] "flutter run" took 70,662ms.
[ +214 ms] 
                    #0      throwToolExit (package:flutter_tools/src/base/common.dart:34:3)
                    #1      RunCommand.runCommand (package:flutter_tools/src/commands/run.dart:867:9)
                    <asynchronous suspension>
                    #2      FlutterCommand.run.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command.dart:1563:27)
                    <asynchronous suspension>
                    #3      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)  
                    <asynchronous suspension>
                    #4      CommandRunner.runCommand (package:args/command_runner.dart:212:13)
                    <asynchronous suspension>
                    #5      FlutterCommandRunner.runCommand.<anonymous closure>
                    (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
                    <asynchronous suspension>
                    #6      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)  
                    <asynchronous suspension>
                    #7      FlutterCommandRunner.runCommand
(package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
                    <asynchronous suspension>
                    #8      run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)    
                    <asynchronous suspension>
                    #9      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)  
                    <asynchronous suspension>
                    #10     main (package:flutter_tools/executable.dart:102:3)
                    <asynchronous suspension>


[  +15 ms] Running 3 shutdown hooks
[  +21 ms] Shutdown hooks complete
[ +280 ms] exiting with code 1
PS C:\Users\<USER>\Desktop\Sepesha_app> 