import 'dart:io';
import 'package:adaptive_theme/adaptive_theme.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:sepesha_app/Utilities/app_theme.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sepesha_app/l10n/app_localizations.dart';
import 'package:sepesha_app/Driver/profile/driver_profile_provider.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/provider/customer_history_provider.dart';
import 'package:sepesha_app/provider/localization_provider.dart';
import 'package:sepesha_app/provider/otp_provider.dart';
import 'package:sepesha_app/provider/payment_provider.dart';
import 'package:sepesha_app/provider/registration_provider.dart';
import 'package:sepesha_app/provider/ride_provider.dart';
import 'package:sepesha_app/provider/message_provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/provider/user_profile_provider.dart';
import 'package:sepesha_app/provider/user_registration_provider.dart';
import 'package:sepesha_app/screens/auth/splash_screen.dart';
import 'package:sepesha_app/screens/info_handler/app_info.dart';
import 'package:sepesha_app/services/auth_services.dart';
import 'package:sepesha_app/services/token_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';


Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();
  await prefs.clear(); 
  await dotenv.load(fileName: ".env");
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Disable SSL certificate verification for testing
  HttpOverrides.global = MyHttpOverrides();

  // Initialize TokenManager to start token refresh scheduling
  await TokenManager.instance.initialize();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LocalizationProvider()),
        ChangeNotifierProvider(create: (_) => UserRegistrationProvider()),
        ChangeNotifierProvider(create: (_) => AppInfo()),
        ChangeNotifierProvider(create: (_) => OTPProvider()),
        ChangeNotifierProvider(create: (_) => RideProvider()),
        ChangeNotifierProvider(create: (_) => PaymentProvider()),
        ChangeNotifierProvider(create: (_) => RegistrationProvider()),
        ChangeNotifierProvider(create: (_) => CustomerHistoryProvider()),
        ChangeNotifierProvider(create: (_) => MessageProvider()),
        ChangeNotifierProvider(create: (_) => UserProfileProvider()),
        ChangeNotifierProvider(create: (_) => DriverProfileProvider()),
      ],
      child: Consumer<LocalizationProvider>(
        builder: (context, localizationProvider, child) {
          return AdaptiveTheme(
            light: AppTheme.lightTheme(context),
            dark: AppTheme.darkTheme(context),
            initial: AdaptiveThemeMode.system,
            builder: (theme, darkTheme) => DynamicColorBuilder(
              builder: (lightDynamic, darkDynamic) {
                return MaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: 'Sepesha',
                  theme: lightDynamic != null
                      ? theme.copyWith(colorScheme: lightDynamic)
                      : theme,
                  darkTheme: darkDynamic != null
                      ? darkTheme.copyWith(colorScheme: darkDynamic)
                      : darkTheme,
                  locale: localizationProvider.locale,
                  localizationsDelegates: [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: const [
                    Locale('en'), // English
                    Locale('sw'), // Swahili
                  ],
                  home: kDebugMode ? SplashScreen() : const SplashScreen(),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
